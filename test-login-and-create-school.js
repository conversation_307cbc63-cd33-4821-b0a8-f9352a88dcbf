const axios = require('axios');

async function loginAndCreateSchool() {
  try {
    console.log('Step 1: Creating a test user (ADMIN role)...');

    // Create user first
    const signUpData = {
      name: '<PERSON>',
      email: '<EMAIL>',
      password: '12345678',
      role: 'admin'  // ADMIN role to create schools
    };

    console.log('Sign-up request data:', JSON.stringify(signUpData, null, 2));

    try {
      const signUpResponse = await axios.post('http://localhost:4000/auth/sign-up', signUpData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('User created successfully!');
      console.log('Sign-up response status:', signUpResponse.status);
      console.log('Sign-up response data:', JSON.stringify(signUpResponse.data, null, 2));
    } catch (signUpError) {
      if (signUpError.response?.status === 400 && signUpError.response?.data?.message?.includes('already exists')) {
        console.log('User already exists, proceeding to login...');
      } else if (signUpError.response?.status === 500) {
        console.log('User creation failed with 500 error (likely user already exists), proceeding to login...');
      } else {
        throw signUpError;
      }
    }

    console.log('\nStep 2: Logging in to get authentication token...');

    // Login to get token
    const loginData = {
      email: '<EMAIL>',
      password: '12345678'
    };

    console.log('Login request data:', JSON.stringify(loginData, null, 2));

    const loginResponse = await axios.post('http://localhost:4000/auth/sign-in', loginData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Login successful!');
    console.log('Login response status:', loginResponse.status);
    console.log('Login response data:', JSON.stringify(loginResponse.data, null, 2));
    
    // Extract token from response
    const token = loginResponse.data?.data?.accessToken || loginResponse.data?.accessToken;
    
    if (!token) {
      console.error('No token found in login response');
      return;
    }
    
    console.log('Token extracted:', token.substring(0, 20) + '...');

    console.log('\nStep 3: Creating school with authenticated token...');
    
    // Create school with token
    const schoolData = {
      name: 'Test School',
      email: '<EMAIL>',
      address: '123 Test Street',
      phoneNumber: '+1234567890'
    };
    
    console.log('School creation request data:', JSON.stringify(schoolData, null, 2));
    
    const createSchoolResponse = await axios.post('http://localhost:4000/schools', schoolData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('School creation successful!');
    console.log('Response status:', createSchoolResponse.status);
    console.log('Response data:', JSON.stringify(createSchoolResponse.data, null, 2));
    
  } catch (error) {
    console.error('Error occurred:');
    console.error('Error message:', error.message);
    console.error('Error code:', error.code);
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response status text:', error.response.statusText);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      
      // Check for JSON parsing errors in response
      if (error.response.data && typeof error.response.data === 'string') {
        console.error('Raw response string:', error.response.data);
        console.error('Response length:', error.response.data.length);
        
        // Try to find the problematic part around position 150
        if (error.response.data.length > 150) {
          console.error('Content around position 150:');
          console.error('Characters 140-160:', JSON.stringify(error.response.data.substring(140, 160)));
        }
      }
    } else if (error.request) {
      console.error('No response received');
      console.error('Request details:', error.request);
    }
    
    // Check if it's a connection error
    if (error.code === 'ECONNREFUSED') {
      console.error('Connection refused - server might not be running on the expected port');
    }
  }
}

loginAndCreateSchool();
